"use client";

import * as React from "react";
import { X, Trash2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { getContrastTextColor } from "@/lib/tag-colors";
import type { Tag } from "@/lib/db";

export interface TagPillProps {
  tag: Tag;
  onRemove?: () => void;
  onEdit?: (tagId: string, newName: string) => Promise<boolean>;
  onDelete?: (tagId: string) => Promise<boolean>;
  className?: string;
  size?: "xs" | "sm" | "md";
  showRemove?: boolean;
  allowInlineEdit?: boolean;
  isCompleted?: boolean;
}

export function TagPill({
  tag,
  onRemove,
  onEdit,
  onDelete,
  className,
  size = "md",
  showRemove = true,
  allowInlineEdit = false,
  isCompleted = false,
}: TagPillProps) {
  const [isEditing, setIsEditing] = React.useState(false);
  const [editedName, setEditedName] = React.useState(tag.name);
  const [isSaving, setIsSaving] = React.useState(false);
  const [error, setError] = React.useState("");
  const inputRef = React.useRef<HTMLInputElement>(null);
  const isDeleteClickingRef = React.useRef(false);

  const textColor = getContrastTextColor(tag.color);

  // Focus input when editing starts
  React.useEffect(() => {
    if (isEditing && inputRef.current) {
      inputRef.current.focus();
      inputRef.current.select();
    }
  }, [isEditing]);

  // Reset edited name when tag changes
  React.useEffect(() => {
    setEditedName(tag.name);
    setError("");
  }, [tag.name]);

  const handleStartEdit = (e: React.MouseEvent) => {
    if (!allowInlineEdit || !onEdit) return;

    e.preventDefault();
    e.stopPropagation();
    setIsEditing(true);
    setEditedName(tag.name);
    setError("");
  };

  const handleBlur = () => {
    // Add a small delay to allow delete button click to be processed first
    setTimeout(() => {
      if (!isDeleteClickingRef.current) {
        handleSaveEdit();
      }
      isDeleteClickingRef.current = false;
    }, 100);
  };

  const handleSaveEdit = async () => {
    if (!onEdit || !editedName.trim()) {
      setError("Tag name cannot be empty");
      return;
    }

    if (editedName.trim() === tag.name) {
      setIsEditing(false);
      setError("");
      return;
    }

    setIsSaving(true);
    setError("");

    try {
      const success = await onEdit(tag.id, editedName.trim());
      if (success) {
        setIsEditing(false);
      } else {
        setError("Tag name already exists or update failed");
      }
    } catch (error) {
      console.error("Error updating tag:", error);
      setError("Failed to update tag");
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setEditedName(tag.name);
    setError("");
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleSaveEdit();
    } else if (e.key === 'Escape') {
      e.preventDefault();
      handleCancelEdit();
    } else if (e.key === 'Delete' && e.ctrlKey && onDelete) {
      e.preventDefault();
      handleDeleteTag();
    }
  };

  const handleDeleteTag = async () => {
    if (!onDelete) return;

    try {
      await onDelete(tag.id);
    } catch (error) {
      console.error("Error deleting tag:", error);
    }
  };

  // Create the main component content
  const component = isEditing ? (
    <div className={cn("inline-flex flex-col gap-1", className)}>
      <div
        className={cn(
          "inline-flex items-center gap-0 rounded-full border",
          size === "xs" ? "px-1.5 py-0.5" : size === "sm" ? "px-2 py-0.5" : "px-2.5 py-1",
        )}
        style={{
          backgroundColor: tag.color,
          borderColor: tag.color,
        }}
      >
        <Input
          ref={inputRef}
          value={editedName}
          onChange={(e) => setEditedName(e.target.value)}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          className={cn(
            "h-auto border-none bg-transparent p-0 font-medium focus-visible:ring-0 focus-visible:ring-offset-0",
            size === "xs" ? "text-xxs min-w-[50px] max-w-[80px]" : size === "sm" ? "text-xs min-w-[60px] max-w-[100px]" : "text-xs min-w-[80px] max-w-[120px]"
          )}
          style={{ color: textColor }}
          disabled={isSaving}
          placeholder="Tag name"
        />

        {onDelete && (
          <Button
            type="button"
            variant="ghost"
            size="sm"
            className="h-auto w-auto p-0 hover:bg-transparent"
            onMouseDown={(e) => {
              e.preventDefault();
              e.stopPropagation();
              isDeleteClickingRef.current = true;
            }}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleDeleteTag();
            }}
            aria-label={`Delete ${tag.name} tag globally`}
            style={{ color: textColor }}
            title="Delete tag (Ctrl+Delete)"
          >
            <Trash2 className={cn(
              "transition-transform hover:scale-110",
              size === "xs" ? "h-2 w-2" : size === "sm" ? "h-2.5 w-2.5" : "h-3 w-3"
            )} />
          </Button>
        )}
      </div>

      {error && (
        <span className="text-xs text-destructive px-2">
          {error}
        </span>
      )}
    </div>
  ) : (
    <div
      data-tag-pill
      className={cn(
        "inline-flex items-center gap-0 rounded-full border transition-colors",
        size === "xs" ? "px-1.5 py-0.5 text-xxs" : size === "sm" ? "px-2 py-0.5 text-xs" : "px-2.5 py-1 text-xs",
        allowInlineEdit && "cursor-pointer hover:opacity-80",
        className
      )}
      style={{
        backgroundColor: tag.color,
        color: textColor,
        borderColor: tag.color,
      }}
      onClick={handleStartEdit}
    >
      <span className="font-medium truncate max-w-[120px]">
        {tag.name}
      </span>

      {showRemove && onRemove && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="h-auto w-auto p-0 hover:bg-transparent"
          onClick={(e) => {
            e.preventDefault();
            e.stopPropagation();
            onRemove();
          }}
          aria-label={`Remove ${tag.name} tag from this task`}
          style={{ color: textColor }}
        >
          <X className={cn(
            "transition-transform hover:scale-110",
            size === "xs" ? "h-2 w-2" : size === "sm" ? "h-2.5 w-2.5" : "h-3 w-3"
          )} />
        </Button>
      )}
    </div>
  );

  return component;
}
